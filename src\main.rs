mod config;
mod dynamic_router;
mod handlers;
mod model_scanner;
mod models;
mod onnx_engine;
mod swagger;

use anyhow::Result;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tower::ServiceBuilder;
use tower_http::cors::CorsLayer;
use tracing::{error, info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::{Modify, OpenApi};
use utoipa_swagger_ui::SwaggerUi;

use config::Config;
use dynamic_router::{create_dynamic_routes, DynamicModelManager};
use handlers::{get_model_info, health_check, load_model, unload_model, AppState};
use model_scanner::ModelScanner;
use onnx_engine::OnnxEngine;
use swagger::{ApiDoc, ServerAddon};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "onnx_service=info,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // 加载配置
    let config = Config::from_env()?;
    info!("Configuration loaded successfully");
    info!("Server will start on: {}", config.server_address());

    // 创建ONNX引擎
    let engine = Arc::new(RwLock::new(OnnxEngine::new()));

    // 扫描并注册动态模型
    let dynamic_manager = DynamicModelManager::new();
    let mut scanner = ModelScanner::new("./models");

    match scanner.scan_and_register().await {
        Ok(uuids) => {
            info!("发现 {} 个UUID模型文件夹", uuids.len());

            // 注册每个发现的模型
            for uuid in uuids {
                if let Some(model) = scanner.get_model_by_uuid(&uuid) {
                    match dynamic_manager.register_model(model.clone()).await {
                        Ok(_) => info!("成功注册动态模型: {}", uuid),
                        Err(e) => error!("注册动态模型失败 {}: {}", uuid, e),
                    }
                }
            }
        }
        Err(e) => {
            warn!("模型扫描失败: {}", e);
            info!("服务将在没有动态模型的情况下启动");
        }
    }

    // 创建应用状态
    let app_state = AppState {
        engine,
        dynamic_manager: dynamic_manager.clone(),
    };

    // 启动定时扫描任务
    start_periodic_scan(
        dynamic_manager.clone(),
        config.scanner.scan_interval_minutes,
    )
    .await;

    // 创建路由
    let app = create_router(app_state, &config, dynamic_manager);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(&config.server_address()).await?;
    info!("🚀 ONNX Service started on {}", config.server_address());
    info!(
        "📖 Swagger UI available at: http://{}/swagger-ui/",
        config.server_address()
    );

    axum::serve(listener, app).await?;

    Ok(())
}

fn create_router(
    app_state: AppState,
    config: &Config,
    dynamic_manager: DynamicModelManager,
) -> Router {
    // 创建动态配置的OpenAPI文档
    let mut openapi = ApiDoc::openapi();
    ServerAddon::new(config.server_address()).modify(&mut openapi);

    // 模型管理路由
    let model_routes = Router::new()
        .route("/info", get(get_model_info))
        .route("/load", post(load_model))
        .route("/unload", post(unload_model));

    // 动态模型路由
    let dynamic_routes = create_dynamic_routes(dynamic_manager);

    // 主路由
    Router::new()
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", openapi))
        .route("/health", get(health_check))
        .nest("/model", model_routes)
        .nest("/models", dynamic_routes) // 动态模型路由前缀
        .with_state(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(CorsLayer::permissive())
                .into_inner(),
        )
}

/// 启动定时扫描任务
async fn start_periodic_scan(dynamic_manager: DynamicModelManager, interval_minutes: u64) {
    info!("启动定时扫描任务，扫描间隔: {} 分钟", interval_minutes);

    let manager = dynamic_manager.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(interval_minutes * 60));
        interval.tick().await; // 跳过第一次立即触发

        loop {
            interval.tick().await;
            info!("开始定时扫描models文件夹...");

            match perform_scan(&manager).await {
                Ok(changes) => {
                    if changes.new_models > 0 || changes.removed_models > 0 {
                        info!(
                            "扫描完成: 新增 {} 个模型, 移除 {} 个模型",
                            changes.new_models, changes.removed_models
                        );
                    } else {
                        info!("扫描完成: 无变化");
                    }
                }
                Err(e) => {
                    error!("定时扫描失败: {}", e);
                }
            }
        }
    });
}

/// 执行扫描并返回变化统计
async fn perform_scan(dynamic_manager: &DynamicModelManager) -> Result<ScanChanges> {
    let mut scanner = ModelScanner::new("./models");

    // 获取当前已注册的模型UUID列表
    let current_uuids = {
        let models = dynamic_manager.models.read().await;
        models
            .keys()
            .cloned()
            .collect::<std::collections::HashSet<_>>()
    };

    // 扫描文件夹获取新的UUID列表
    let discovered_uuids = match scanner.scan_and_register().await {
        Ok(uuids) => uuids.into_iter().collect::<std::collections::HashSet<_>>(),
        Err(e) => {
            return Err(anyhow::anyhow!("扫描失败: {}", e));
        }
    };

    // 计算新增的模型
    let new_uuids: Vec<_> = discovered_uuids
        .difference(&current_uuids)
        .cloned()
        .collect();

    // 计算需要移除的模型
    let removed_uuids: Vec<_> = current_uuids
        .difference(&discovered_uuids)
        .cloned()
        .collect();

    // 注册新模型
    for uuid in &new_uuids {
        if let Some(model) = scanner.get_model_by_uuid(uuid) {
            match dynamic_manager.register_model(model.clone()).await {
                Ok(_) => info!("定时扫描: 成功注册新模型 {}", uuid),
                Err(e) => error!("定时扫描: 注册模型失败 {}: {}", uuid, e),
            }
        }
    }

    // 移除已删除的模型
    for uuid in &removed_uuids {
        match dynamic_manager.unregister_model(uuid).await {
            Ok(_) => info!("定时扫描: 成功移除模型 {}", uuid),
            Err(e) => error!("定时扫描: 移除模型失败 {}: {}", uuid, e),
        }
    }

    Ok(ScanChanges {
        new_models: new_uuids.len(),
        removed_models: removed_uuids.len(),
    })
}

/// 扫描变化统计
struct ScanChanges {
    new_models: usize,
    removed_models: usize,
}
