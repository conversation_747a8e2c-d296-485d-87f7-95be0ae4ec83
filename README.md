# ONNX Service

基于Rust的ONNX模型推理API服务框架，支持动态模型注册和自动API生成。

## 📋 目录
- [功能概述](#功能概述)
- [ONNX技术栈介绍](#onnx技术栈介绍)
- [快速开始](#快速开始)
- [API接口说明](#api接口说明)
- [配置说明](#配置说明)

## 功能概述

- **传统模型管理**: 支持手动加载/卸载ONNX模型
- **动态模型注册**: 基于UUID文件夹自动扫描和API生成
- **定时扫描**: 自动检测模型文件变化并更新API
- **RESTful API**: 统一的HTTP接口，集成Swagger文档

## ONNX技术栈介绍

### ONNX简介
ONNX (Open Neural Network Exchange) 是一个开放的神经网络交换格式，允许不同深度学习框架之间的模型互操作。它定义了一套通用的计算图表示，使得在PyTorch、TensorFlow、Keras等框架训练的模型可以转换为统一格式进行部署。

### ONNX、ONNX Runtime、ORT关系
- **ONNX**: 模型格式标准，定义了神经网络的结构和参数存储方式
- **ONNX Runtime**: Microsoft开发的高性能推理引擎，用于执行ONNX模型
- **ORT**: ONNX Runtime的Rust绑定库，提供Rust语言的API接口

### 版本对应关系
本项目使用：
- **ORT版本**: 2.0.0-rc.10 (Rust crate)
- **ONNX Runtime版本**: 1.22.1 (底层C++库)
- **对应关系**: ORT 2.0.0-rc.10 兼容 ONNX Runtime 1.22.1

### ONNX Runtime配置说明
由于ONNX Runtime是C++库，需要正确配置动态链接库：

#### 1. **为什么需要配置**: Rust通过FFI调用C++库，需要在运行时找到onnxruntime.dll
#### 2. **配置位置**: `.cargo/config.toml`文件中设置库路径
##### 1. 环境变量配置 ([env] 部分)
- ORT_STRATEGY = "system": 告诉 ort crate 使用系统已安装的 ONNX Runtime 库，而不是自动下载
- ORT_LIB_LOCATION: 指定 ONNX Runtime 库的位置

## 快速开始

### 1. 环境要求
- Rust 1.70+
- Windows x64平台
- ONNX Runtime 1.22.1 (需要手动下载配置)

### 2. ONNX Runtime配置
下载并配置ONNX Runtime：
```bash
# 1. 下载ONNX Runtime 1.22.1 for Windows x64
# 从 https://github.com/microsoft/onnxruntime/releases/tag/v1.22.1
# 下载 onnxruntime-win-x64-1.22.1.zip

# 2. 解压到本地目录，例如：
# C:\Users\<USER>\Desktop\onnxruntime-win-x64-1.22.1\

# 3. 配置.cargo/config.toml (已包含在项目中)
# 修改其中的路径为你的实际路径
```

### 3. 启动服务
```bash
# 复制配置文件
cp .env.example .env

# 构建并运行
cargo build --release
./target/release/onnx_service.exe
```

访问地址：
- **API服务**: http://127.0.0.1:8081
- **Swagger文档**: http://127.0.0.1:8081/swagger-ui/
- **健康检查**: http://127.0.0.1:8081/health

## API接口说明

本服务提供两类API接口：**全局接口**（用于服务管理）和**动态生成接口**（基于UUID模型自动生成）。

### 全局接口

#### 1. 健康检查
```http
GET /health
```
检查服务运行状态和模型加载状态。

#### 2. 获取所有模型信息
```http
GET /model/info
```
返回所有已注册模型的信息，包括传统模型和动态模型。

**响应示例**:
```json
{
  "traditional_model": {
    "name": "default_model",
    "path": "./models/model.onnx",
    "is_loaded": true
  },
  "dynamic_models": [
    {
      "uuid": "550e8400-e29b-41d4-a716-************",
      "config": {...},
      "onnx_path": "./models/550e8400-e29b-41d4-a716-************/model.onnx",
      "status": "loaded",
      "is_loaded": true
    }
  ],
  "total_count": 2
}
```

#### 3. 手动加载模型
```http
POST /model/load
Content-Type: application/json

{
  "folder_name": "550e8400-e29b-41d4-a716-************"
}
```

或者使用模型名称_UUID格式：
```http
POST /model/load
Content-Type: application/json

{
  "folder_name": "Al243_uts_v1_550e8400-e29b-41d4-a716-************"
}
```

加载指定的UUID文件夹模型。只需提供文件夹名称，支持两种文件夹命名格式：
- **纯UUID格式**: `550e8400-e29b-41d4-a716-************`
- **模型名称_UUID格式**: `Al243_uts_v1_550e8400-e29b-41d4-a716-************`

所有模型都必须按照UUID文件夹格式组织（包含model.onnx和config.json）。

#### 4. 手动卸载模型

支持两种卸载方式，二选一即可：

**方式1：根据UUID卸载**
```http
POST /model/unload
Content-Type: application/json

{
  "uuid": "550e8400-e29b-41d4-a716-************"
}
```

**方式2：根据模型名称卸载**
```http
POST /model/unload
Content-Type: application/json

{
  "model_name": "Al243_uts_v1"
}
```

卸载指定的模型。可以通过以下两种方式之一进行卸载：
- **UUID方式**: 提供模型的UUID（从文件夹名称中提取的UUID部分）
- **模型名称方式**: 提供模型的名称（config.json中的name字段）

注意：不能同时提供uuid和model_name参数。

### 动态生成接口

基于UUID文件夹自动生成的模型专用接口。

#### 1. 模型信息查看
```http
GET /models/{uuid}/info
```
获取特定UUID模型的配置信息。

**响应示例**:
```json
{
  "uuid": "550e8400-e29b-41d4-a716-************",
  "config": {
    "name": "Ultimate Tensile Strength Prediction",
    "version": "1.0.0",
    "inputs": [
      {"feature_name": "Mg", "min": 0, "max": 100, "description": "amount of Mg"},
      {"feature_name": "Al", "min": 0, "max": 100, "description": "amount of Al"}
    ],
    "outputs": [
      {"target": "Ultimate Tensile Strength", "min": 0, "max": 1000, "description": "predicted strength"}
    ]
  },
  "status": "loaded",
  "is_loaded": true
}
```

#### 2. 模型推理
```http
POST /models/{uuid}/predict
Content-Type: application/json

{
  "inputs": {
    "Mg": 5.2,
    "Al": 85.5,
    "Si": 7.3,
    "Fe": 2.8
  }
}
```

**响应示例**:
```json
{
  "outputs": {
    "Ultimate Tensile Strength": 312.76
  },
  "inference_time_ms": 0.65,
  "request_id": "uuid-string",
  "model_uuid": "550e8400-e29b-41d4-a716-************"
}
```

### 模型文件夹结构

动态模型需要按以下结构组织，支持两种文件夹命名格式：

```
models/
├── 550e8400-e29b-41d4-a716-************/              # 纯UUID格式
│   ├── model.onnx                                     # ONNX模型文件
│   └── config.json                                    # 模型配置文件
├── Al243_uts_v1_550e8400-e29b-41d4-a716-************/ # 模型名称_UUID格式
│   ├── model.onnx                                     # ONNX模型文件
│   └── config.json                                    # 模型配置文件
```

**文件夹命名规则**：
- **纯UUID格式**: `550e8400-e29b-41d4-a716-************`
- **模型名称_UUID格式**: `模型名称_550e8400-e29b-41d4-a716-************`
  - 模型名称部分可以包含字母、数字、下划线等字符
  - 使用下划线 `_` 分隔模型名称和UUID
  - UUID部分必须是有效的UUID格式（位于最后）

**config.json示例**:
```json
{
  "name": "Ultimate Tensile Strength Prediction",
  "version": "1.0.0",
  "description": "Predicts ultimate tensile strength of aluminum alloys",
  "inputs": [
    {"feature_name": "Mg", "min": 0, "max": 100, "description": "amount of Mg, unit is weight percent"},
    {"feature_name": "Al", "min": 0, "max": 100, "description": "amount of Al, unit is weight percent"}
  ],
  "outputs": [
    {"target": "Ultimate Tensile Strength", "min": 0, "max": 1000, "description": "predicted tensile strength in MPa"}
  ]
}
```

## 配置说明

### 环境变量配置

| 变量名                      | 描述                     | 默认值              |
| --------------------------- | ------------------------ | ------------------- |
| SERVER_HOST                 | 服务器绑定地址           | 127.0.0.1           |
| SERVER_PORT                 | 服务器端口               | 8081                |
| MODEL_PATH                  | 默认模型文件路径         | ./models/model.onnx |
| MODEL_NAME                  | 默认模型名称             | default_model       |
| MODEL_SCAN_INTERVAL_MINUTES | 模型扫描间隔（分钟）     | 1                   |
| INTRA_THREADS               | ONNX Runtime内部线程数   | 4                   |
| INTER_THREADS               | ONNX Runtime操作间线程数 | 1                   |
| OPTIMIZATION_LEVEL          | 图优化级别               | 3                   |
