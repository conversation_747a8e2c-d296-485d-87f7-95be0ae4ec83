use utoipa::{
    openapi::security::{Api<PERSON>ey, <PERSON>pi<PERSON>ey<PERSON><PERSON>ue, SecurityScheme},
    Modify, OpenApi,
};

use crate::models::{
    AllModelsInfoResponse, DynamicInferenceRequest, DynamicInferenceResponse, DynamicModelInfo,
    DynamicOutputValue, ErrorResponse, HealthResponse, InputSpec, LoadModelRequest,
    LoadModelResponse, ModelConfig, ModelConfigResponse, ModelInfo, ModelMetadata, OutputSpec,
    TensorData, TensorInfo, UnloadModelRequest,
};

#[derive(OpenApi)]
#[openapi(
    paths(
        crate::handlers::health_check,
        crate::handlers::get_model_info,
        crate::handlers::load_model,
        crate::handlers::unload_model,
    ),
    components(
        schemas(
            ModelInfo,
            HealthResponse,
            ErrorResponse,
            LoadModelResponse,
            TensorData,
            TensorInfo,
            DynamicInferenceRequest,
            DynamicInferenceResponse,
            DynamicOutputValue,
            ModelConfig,
            ModelConfigResponse,
            ModelMetadata,
            InputSpec,
            OutputSpec,
            AllModelsInfoResponse,
            DynamicModelInfo,
            LoadModelRequest,
            UnloadModelRequest,
        )
    ),
    tags(
        (name = "Health", description = "Health check endpoints"),
        (name = "Model", description = "Model management endpoints"),
        (name = "Inference", description = "Inference endpoints"),
        (name = "Dynamic Models", description = "Dynamic model endpoints based on UUID")
    ),
    info(
        title = "ONNX Service API",
        version = "1.0.0",
        description = "A RESTful API service for ONNX model inference",
        contact(
            name = "ONNX Service",
            email = "<EMAIL>"
        ),
        license(
            name = "MIT",
            url = "https://opensource.org/licenses/MIT"
        )
    )
)]
pub struct ApiDoc;

/// 自定义OpenAPI修改器
pub struct SecurityAddon;

impl Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        if let Some(components) = openapi.components.as_mut() {
            components.add_security_scheme(
                "api_key",
                SecurityScheme::ApiKey(ApiKey::Header(ApiKeyValue::new("X-API-Key"))),
            )
        }
    }
}

/// 动态服务器地址修改器
pub struct ServerAddon {
    pub server_address: String,
}

impl ServerAddon {
    pub fn new(server_address: String) -> Self {
        Self { server_address }
    }
}

impl Modify for ServerAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        use utoipa::openapi::ServerBuilder;

        let server = ServerBuilder::new()
            .url(format!("http://{}", self.server_address))
            .description(Some("Dynamic server based on configuration"))
            .build();

        openapi.servers = Some(vec![server]);
    }
}
