use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub scanner: ScannerConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScannerConfig {
    pub scan_interval_minutes: u64,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenv::dotenv().ok();

        let server = ServerConfig {
            host: env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("SERVER_PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()?,
        };

        let scanner = ScannerConfig {
            scan_interval_minutes: env::var("MODEL_SCAN_INTERVAL_MINUTES")
                .unwrap_or_else(|_| "1".to_string())
                .parse()?,
        };

        Ok(Config { server, scanner })
    }

    pub fn server_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }
}
