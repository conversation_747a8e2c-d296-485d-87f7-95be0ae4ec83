use anyhow::{anyhow, Result};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info};

use crate::models::{
    DynamicInferenceRequest, DynamicInferenceResponse, DynamicOutputValue, ErrorResponse,
    ModelConfigResponse, ModelInfo, RegisteredModel, TensorData,
};
use crate::onnx_engine::{OnnxEngine, SharedOnnxEngine};

/// 动态模型管理器
#[derive(Clone)]
pub struct DynamicModelManager {
    /// 已注册的模型
    pub models: Arc<RwLock<HashMap<String, RegisteredModel>>>,
    /// 每个模型的ONNX引擎实例
    pub engines: Arc<RwLock<HashMap<String, SharedOnnxEngine>>>,
}

impl DynamicModelManager {
    /// 创建新的动态模型管理器
    pub fn new() -> Self {
        Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            engines: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 注册模型
    pub async fn register_model(&self, model: RegisteredModel) -> Result<()> {
        let uuid = model.uuid.clone();
        info!("注册模型: {} (UUID: {})", model.config.model.name, uuid);

        // 创建专用的ONNX引擎
        let engine = Arc::new(RwLock::new(OnnxEngine::new()));

        // 加载模型
        {
            let mut engine_guard = engine.write().await;
            engine_guard
                .load_model(&model.onnx_path, Some(model.config.model.name.clone()))
                .await
                .map_err(|e| anyhow!("Failed to load model {}: {}", uuid, e))?;
        }

        // 存储模型和引擎
        {
            let mut models = self.models.write().await;
            models.insert(uuid.clone(), model);
        }
        {
            let mut engines = self.engines.write().await;
            engines.insert(uuid.clone(), engine);
        }

        info!("模型 {} 注册成功", uuid);
        Ok(())
    }

    /// 获取模型配置
    pub async fn get_model_config(&self, uuid: &str) -> Option<RegisteredModel> {
        let models = self.models.read().await;
        models.get(uuid).cloned()
    }

    /// 根据模型名称查找UUID
    pub async fn find_uuid_by_model_name(&self, model_name: &str) -> Option<String> {
        let models = self.models.read().await;
        for (uuid, model) in models.iter() {
            if model.config.model.name == model_name {
                return Some(uuid.clone());
            }
        }
        None
    }

    /// 执行模型推理
    pub async fn inference(
        &self,
        uuid: &str,
        request: DynamicInferenceRequest,
    ) -> Result<DynamicInferenceResponse> {
        // 获取模型配置
        let model = {
            let models = self.models.read().await;
            models
                .get(uuid)
                .cloned()
                .ok_or_else(|| anyhow!("Model not found: {}", uuid))?
        };

        // 获取引擎
        let engine = {
            let engines = self.engines.read().await;
            engines
                .get(uuid)
                .cloned()
                .ok_or_else(|| anyhow!("Engine not found for model: {}", uuid))?
        };

        // 验证输入
        self.validate_inputs(&model, &request.inputs)?;

        // 获取模型信息以获取正确的输入名称
        let model_info = {
            let engine_guard = engine.read().await;
            engine_guard
                .get_model_info()
                .cloned()
                .ok_or_else(|| anyhow!("Model info not available"))?
        };

        // 转换输入格式
        let onnx_inputs = self.convert_to_onnx_inputs(&model, request.inputs, &model_info)?;

        // 执行推理
        let start_time = std::time::Instant::now();
        let onnx_outputs = {
            let mut engine_guard = engine.write().await;
            engine_guard
                .inference_with_tensors(onnx_inputs)
                .await
                .map_err(|e| anyhow!("Inference failed: {}", e))?
        };

        // 转换输出格式
        let outputs = self.convert_from_onnx_response(&model, onnx_outputs)?;
        let inference_time = start_time.elapsed().as_secs_f64() * 1000.0;

        Ok(DynamicInferenceResponse {
            outputs,
            inference_time_ms: inference_time,
            request_id: uuid::Uuid::new_v4().to_string(),
            model_uuid: uuid.to_string(),
        })
    }

    /// 验证输入数据
    fn validate_inputs(
        &self,
        model: &RegisteredModel,
        inputs: &HashMap<String, f64>,
    ) -> Result<()> {
        // 检查所有必需的输入是否都提供了
        for input_spec in &model.config.inputs {
            let value = inputs
                .get(&input_spec.feature)
                .ok_or_else(|| anyhow!("Missing required input: {}", input_spec.feature))?;

            // 检查值是否在有效范围内
            if *value < input_spec.min || *value > input_spec.max {
                return Err(anyhow!(
                    "Input '{}' value {} is out of range [{}, {}]",
                    input_spec.feature,
                    value,
                    input_spec.min,
                    input_spec.max
                ));
            }
        }

        // 检查是否有多余的输入
        for input_name in inputs.keys() {
            if !model
                .config
                .inputs
                .iter()
                .any(|spec| &spec.feature == input_name)
            {
                return Err(anyhow!("Unknown input: {}", input_name));
            }
        }

        Ok(())
    }

    /// 转换为ONNX推理输入格式
    fn convert_to_onnx_inputs(
        &self,
        model: &RegisteredModel,
        inputs: HashMap<String, f64>,
        model_info: &ModelInfo,
    ) -> Result<HashMap<String, TensorData>> {
        let mut onnx_inputs = HashMap::new();

        // 按照模型配置的顺序构建输入数据
        let mut input_data = Vec::new();
        for input_spec in &model.config.inputs {
            let value = inputs
                .get(&input_spec.feature)
                .ok_or_else(|| anyhow!("Missing input: {}", input_spec.feature))?;
            input_data.push(*value as f32);
        }

        // 创建张量数据（假设是1D输入）
        let tensor_data = TensorData::new(
            "float32".to_string(),
            vec![1, input_data.len() as i64], // batch_size=1, feature_count
            input_data,
        );

        // 使用模型的第一个输入名称作为张量名称
        let input_name = if !model_info.inputs.is_empty() {
            model_info.inputs[0].name.clone()
        } else {
            "input".to_string() // 回退到默认名称
        };
        onnx_inputs.insert(input_name, tensor_data);

        Ok(onnx_inputs)
    }

    /// 转换ONNX响应格式
    fn convert_from_onnx_response(
        &self,
        model: &RegisteredModel,
        onnx_outputs: HashMap<String, TensorData>,
    ) -> Result<HashMap<String, DynamicOutputValue>> {
        let mut outputs = HashMap::new();

        // 获取第一个输出张量的数据
        if let Some((_, tensor_data)) = onnx_outputs.iter().next() {
            if tensor_data.data.len() != model.config.outputs.len() {
                return Err(anyhow!(
                    "Output dimension mismatch: expected {}, got {}",
                    model.config.outputs.len(),
                    tensor_data.data.len()
                ));
            }

            // 将输出数据映射到配置的输出名称
            for (i, output_spec) in model.config.outputs.iter().enumerate() {
                if let Some(value) = tensor_data.data.get(i) {
                    let output_value = DynamicOutputValue {
                        value: *value as f64,
                        description: output_spec.description.clone(),
                        min: output_spec.min,
                        max: output_spec.max,
                    };
                    outputs.insert(output_spec.target.clone(), output_value);
                }
            }
        } else {
            return Err(anyhow!("No output data received from model"));
        }

        Ok(outputs)
    }

    // 注意：get_registered_uuids 方法已移除，如需获取UUID列表，请使用 AllModelsInfoResponse

    /// 注销模型
    pub async fn unregister_model(&self, uuid: &str) -> Result<()> {
        info!("注销模型: UUID={}", uuid);

        // 从模型列表中移除
        {
            let mut models = self.models.write().await;
            if models.remove(uuid).is_none() {
                return Err(anyhow!("Model not found: {}", uuid));
            }
        }

        // 从引擎列表中移除
        {
            let mut engines = self.engines.write().await;
            engines.remove(uuid);
        }

        info!("模型 {} 注销成功", uuid);
        Ok(())
    }
}

/// 创建动态路由
pub fn create_dynamic_routes<S>(manager: DynamicModelManager) -> Router<S>
where
    S: Clone + Send + Sync + 'static,
{
    Router::new()
        .route("/:uuid/inference", post(dynamic_inference))
        .route("/:uuid/info", get(get_model_config))
        .with_state(manager)
}

/// 动态模型推理处理器
#[utoipa::path(
    post,
    path = "/models/{uuid}/inference",
    params(
        ("uuid" = String, Path, description = "Model UUID")
    ),
    request_body = DynamicInferenceRequest,
    responses(
        (status = 200, description = "Inference successful", body = DynamicInferenceResponse),
        (status = 404, description = "Model not found", body = ErrorResponse),
        (status = 500, description = "Inference failed", body = ErrorResponse)
    ),
    tag = "Dynamic Models"
)]
async fn dynamic_inference(
    Path(uuid): Path<String>,
    State(manager): State<DynamicModelManager>,
    Json(request): Json<DynamicInferenceRequest>,
) -> Result<Json<DynamicInferenceResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("执行模型推理: UUID={}", uuid);

    match manager.inference(&uuid, request).await {
        Ok(response) => {
            info!(
                "推理完成: UUID={}, 耗时={:.2}ms",
                uuid, response.inference_time_ms
            );
            Ok(Json(response))
        }
        Err(e) => {
            error!("推理失败: UUID={}, 错误={}", uuid, e);
            let error = ErrorResponse {
                error_code: "DYNAMIC_INFERENCE_FAILED".to_string(),
                message: format!("Dynamic inference failed: {}", e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error)))
        }
    }
}

/// 获取模型配置处理器
#[utoipa::path(
    get,
    path = "/models/{uuid}/info",
    params(
        ("uuid" = String, Path, description = "Model UUID")
    ),
    responses(
        (status = 200, description = "Model configuration", body = ModelConfigResponse),
        (status = 404, description = "Model not found", body = ErrorResponse)
    ),
    tag = "Dynamic Models"
)]
async fn get_model_config(
    Path(uuid): Path<String>,
    State(manager): State<DynamicModelManager>,
) -> Result<Json<ModelConfigResponse>, (StatusCode, Json<ErrorResponse>)> {
    match manager.get_model_config(&uuid).await {
        Some(model) => {
            let response = ModelConfigResponse {
                uuid: uuid.clone(),
                config: model.config,
                status: "loaded".to_string(),
                is_loaded: true,
            };
            Ok(Json(response))
        }
        None => {
            let error = ErrorResponse {
                error_code: "MODEL_NOT_FOUND".to_string(),
                message: format!("Model not found: {}", uuid),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::NOT_FOUND, Json(error)))
        }
    }
}
