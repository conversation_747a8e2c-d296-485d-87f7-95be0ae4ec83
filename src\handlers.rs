use axum::{extract::State, http::StatusCode, response::J<PERSON>};
use tracing::{error, info};

use crate::dynamic_router::DynamicModelManager;
use crate::models::{
    AllModelsInfoResponse, DynamicModelInfo, ErrorResponse, HealthResponse, LoadModelRequest,
    LoadModelResponse, UnloadModelRequest,
};
use crate::onnx_engine::SharedOnnxEngine;

/// 应用状态
#[derive(Clone)]
pub struct AppState {
    pub engine: SharedOnnxEngine,
    pub dynamic_manager: DynamicModelManager,
}

/// 健康检查端点
#[utoipa::path(
    get,
    path = "/health",
    responses(
        (status = 200, description = "Service is healthy", body = HealthResponse)
    ),
    tag = "Health"
)]
pub async fn health_check(
    State(state): State<AppState>,
) -> Result<Json<HealthResponse>, (StatusCode, Json<ErrorResponse>)> {
    let engine = state.engine.read().await;

    let model_status = if engine.is_loaded() {
        "loaded"
    } else {
        "not_loaded"
    };

    let response = HealthResponse {
        model_status: model_status.to_string(),
        ..Default::default()
    };

    Ok(Json(response))
}

/// 获取所有模型信息端点
#[utoipa::path(
    get,
    path = "/model/info",
    responses(
        (status = 200, description = "All models information", body = AllModelsInfoResponse),
        (status = 200, description = "No models loaded", body = AllModelsInfoResponse)
    ),
    tag = "Model"
)]
pub async fn get_model_info(
    State(state): State<AppState>,
) -> Result<Json<AllModelsInfoResponse>, (StatusCode, Json<ErrorResponse>)> {
    // 获取传统模型信息
    let traditional_model = {
        let engine = state.engine.read().await;
        engine.get_model_info().cloned()
    };

    // 获取动态模型信息
    let dynamic_models = {
        let models = state.dynamic_manager.models.read().await;
        let mut dynamic_model_infos = Vec::new();

        for (uuid, registered_model) in models.iter() {
            let dynamic_model_info = DynamicModelInfo {
                uuid: uuid.clone(),
                config: registered_model.config.clone(),
                onnx_path: registered_model.onnx_path.clone(),
                status: "loaded".to_string(),
                is_loaded: true,
            };
            dynamic_model_infos.push(dynamic_model_info);
        }

        dynamic_model_infos
    };

    let total_count = if traditional_model.is_some() { 1 } else { 0 } + dynamic_models.len();

    let response = AllModelsInfoResponse {
        traditional_model,
        dynamic_models,
        total_count,
    };

    Ok(Json(response))
}

/// 加载模型端点
/// 所有模型都必须按照UUID文件夹格式组织（包含model.onnx和config.json）
#[utoipa::path(
    post,
    path = "/model/load",
    request_body = LoadModelRequest,
    responses(
        (status = 200, description = "Model loaded successfully", body = LoadModelResponse),
        (status = 400, description = "Invalid request", body = ErrorResponse),
        (status = 500, description = "Internal server error", body = ErrorResponse)
    ),
    tag = "Model"
)]
pub async fn load_model(
    State(state): State<AppState>,
    Json(request): Json<LoadModelRequest>,
) -> Result<Json<LoadModelResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Loading model from folder: {}", request.folder_name);

    // 验证UUID格式（支持 UUID 或 模型名称_UUID 格式）
    let _uuid_part = extract_uuid_from_folder_name(&request.folder_name)?;

    // 构建完整路径
    let model_path = format!("./models/{}", request.folder_name);

    // 加载UUID模型
    match load_uuid_model(&state, &model_path).await {
        Ok(model_info) => {
            let response = LoadModelResponse {
                success: true,
                message: format!("Model loaded successfully: {}", model_info.uuid),
                model_info: None, // UUID模型不返回传统ModelInfo
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to load model: {}", e);
            let error = ErrorResponse {
                error_code: "MODEL_LOAD_FAILED".to_string(),
                message: format!("Failed to load model: {}", e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error)))
        }
    }
}

/// 卸载模型端点
/// 模型名称或者UUID(二选一)
#[utoipa::path(
    post,
    path = "/model/unload",
    request_body = UnloadModelRequest,
    responses(
        (status = 200, description = "Model unloaded successfully"),
        (status = 404, description = "Model not found", body = ErrorResponse),
        (status = 400, description = "Invalid request", body = ErrorResponse)
    ),
    tag = "Model"
)]
pub async fn unload_model(
    State(state): State<AppState>,
    Json(request): Json<UnloadModelRequest>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    // 验证请求参数
    if request.uuid.is_none() && request.model_name.is_none() {
        let error = ErrorResponse {
            error_code: "INVALID_REQUEST".to_string(),
            message: "Either 'uuid' or 'model_name' must be provided".to_string(),
            request_id: Some(uuid::Uuid::new_v4().to_string()),
        };
        return Err((StatusCode::BAD_REQUEST, Json(error)));
    }

    if request.uuid.is_some() && request.model_name.is_some() {
        let error = ErrorResponse {
            error_code: "INVALID_REQUEST".to_string(),
            message: "Only one of 'uuid' or 'model_name' should be provided".to_string(),
            request_id: Some(uuid::Uuid::new_v4().to_string()),
        };
        return Err((StatusCode::BAD_REQUEST, Json(error)));
    }

    // 确定要卸载的UUID
    let target_uuid = if let Some(uuid) = &request.uuid {
        info!("Unloading model by UUID: {}", uuid);
        uuid.clone()
    } else if let Some(model_name) = &request.model_name {
        info!("Unloading model by name: {}", model_name);
        match state
            .dynamic_manager
            .find_uuid_by_model_name(model_name)
            .await
        {
            Some(uuid) => {
                info!("Found UUID {} for model name: {}", uuid, model_name);
                uuid
            }
            None => {
                let error = ErrorResponse {
                    error_code: "MODEL_NOT_FOUND".to_string(),
                    message: format!("No model found with name: {}", model_name),
                    request_id: Some(uuid::Uuid::new_v4().to_string()),
                };
                return Err((StatusCode::NOT_FOUND, Json(error)));
            }
        }
    } else {
        unreachable!("This should not happen due to validation above");
    };

    // 卸载模型
    match state.dynamic_manager.unregister_model(&target_uuid).await {
        Ok(_) => {
            info!("Model unloaded successfully: {}", target_uuid);
            Ok(StatusCode::OK)
        }
        Err(e) => {
            error!("Failed to unload model: {}", e);
            let error = ErrorResponse {
                error_code: "MODEL_UNLOAD_FAILED".to_string(),
                message: format!("Failed to unload model: {}", e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::NOT_FOUND, Json(error)))
        }
    }
}

/// 加载UUID模型的辅助函数
async fn load_uuid_model(
    state: &AppState,
    uuid_folder_path: &str,
) -> Result<DynamicModelInfo, anyhow::Error> {
    use crate::model_scanner::ModelScanner;
    use std::path::Path;

    let path = Path::new(uuid_folder_path);

    // 获取文件夹名称
    let folder_name = path
        .file_name()
        .and_then(|name| name.to_str())
        .ok_or_else(|| anyhow::anyhow!("Invalid UUID folder path"))?;

    // 提取UUID部分（支持 UUID 或 UUID_模型名称 格式）
    let uuid = extract_uuid_from_folder_name_simple(folder_name)
        .map_err(|_| anyhow::anyhow!("Invalid UUID format: {}", folder_name))?;

    // 检查文件夹是否存在
    if !path.exists() || !path.is_dir() {
        return Err(anyhow::anyhow!(
            "UUID folder does not exist: {}",
            uuid_folder_path
        ));
    }

    // 使用ModelScanner扫描单个文件夹
    let scanner = ModelScanner::new(path.parent().unwrap_or(Path::new(".")));

    // 扫描特定的UUID文件夹
    match scanner.scan_model_directory(path, &uuid).await {
        Ok(registered_model) => {
            // 注册到动态管理器
            state
                .dynamic_manager
                .register_model(registered_model.clone())
                .await?;

            // 返回动态模型信息
            Ok(DynamicModelInfo {
                uuid: registered_model.uuid,
                config: registered_model.config,
                onnx_path: registered_model.onnx_path,
                status: "loaded".to_string(),
                is_loaded: true,
            })
        }
        Err(e) => Err(anyhow::anyhow!("Failed to scan UUID model: {}", e)),
    }
}

/// 从文件夹名称中提取UUID
/// 支持两种格式：
/// 1. 纯UUID: "550e8400-e29b-41d4-a716-************"
/// 2. 模型名称_UUID: "Al243_uts_v1_550e8400-e29b-41d4-a716-************"
fn extract_uuid_from_folder_name(
    folder_name: &str,
) -> Result<String, (StatusCode, Json<ErrorResponse>)> {
    // 首先尝试直接解析为UUID
    if uuid::Uuid::parse_str(folder_name).is_ok() {
        return Ok(folder_name.to_string());
    }

    // 如果不是纯UUID，尝试提取UUID部分（假设格式为 模型名称_UUID）
    if let Some(underscore_pos) = folder_name.rfind('_') {
        let uuid_part = &folder_name[underscore_pos + 1..];
        if uuid::Uuid::parse_str(uuid_part).is_ok() {
            return Ok(uuid_part.to_string());
        }
    }

    // 如果都不匹配，返回错误
    let error = ErrorResponse {
        error_code: "INVALID_UUID_FORMAT".to_string(),
        message: format!(
            "Folder name '{}' must be a valid UUID or ModelName_UUID format",
            folder_name
        ),
        request_id: Some(uuid::Uuid::new_v4().to_string()),
    };
    Err((StatusCode::BAD_REQUEST, Json(error)))
}

/// 从文件夹名称中提取UUID（简单版本，用于内部使用）
/// 支持两种格式：
/// 1. 纯UUID: "550e8400-e29b-41d4-a716-************"
/// 2. 模型名称_UUID: "Al243_uts_v1_550e8400-e29b-41d4-a716-************"
fn extract_uuid_from_folder_name_simple(folder_name: &str) -> Result<String, ()> {
    // 首先尝试直接解析为UUID
    if uuid::Uuid::parse_str(folder_name).is_ok() {
        return Ok(folder_name.to_string());
    }

    // 如果不是纯UUID，尝试提取UUID部分（假设格式为 模型名称_UUID）
    if let Some(underscore_pos) = folder_name.rfind('_') {
        let uuid_part = &folder_name[underscore_pos + 1..];
        if uuid::Uuid::parse_str(uuid_part).is_ok() {
            return Ok(uuid_part.to_string());
        }
    }

    // 如果都不匹配，返回错误
    Err(())
}
